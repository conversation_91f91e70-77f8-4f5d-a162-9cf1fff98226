use std::io;
use tracing::Level;
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_subscriber::{
    fmt::{self, time::ChronoUtc},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};

/// 日志配置
#[derive(Debug, Clone)]
pub struct LogConfig {
    pub level: Level,
    pub log_to_file: bool,
    pub log_dir: String,
    pub file_prefix: String,
    pub rotation: Rotation,
    pub show_target: bool,
    pub show_thread_ids: bool,
    pub show_line_number: bool,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: Level::INFO,
            log_to_file: true,
            log_dir: "logs".to_string(),
            file_prefix: "trading-ui".to_string(),
            rotation: Rotation::DAILY,
            show_target: true,
            show_thread_ids: false,
            show_line_number: true,
        }
    }
}

/// 初始化日志系统
pub fn init_logging(config: LogConfig) -> Result<(), Box<dyn std::error::Error>> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(config.level.to_string()));

    let registry = tracing_subscriber::registry().with(env_filter);

    if config.log_to_file {
        // 创建日志目录
        std::fs::create_dir_all(&config.log_dir)?;

        // 文件日志层
        let file_appender = RollingFileAppender::new(
            config.rotation,
            &config.log_dir,
            &config.file_prefix,
        );

        let file_layer = fmt::layer()
            .with_writer(file_appender)
            .with_timer(ChronoUtc::rfc_3339())
            .with_target(config.show_target)
            .with_thread_ids(config.show_thread_ids)
            .with_line_number(config.show_line_number)
            .with_ansi(false); // 文件中不使用颜色

        // 控制台日志层
        let console_layer = fmt::layer()
            .with_writer(io::stdout)
            .with_timer(ChronoUtc::rfc_3339())
            .with_target(config.show_target)
            .with_thread_ids(config.show_thread_ids)
            .with_line_number(config.show_line_number)
            .with_ansi(true); // 控制台使用颜色

        registry.with(file_layer).with(console_layer).init();
    } else {
        // 仅控制台日志
        let console_layer = fmt::layer()
            .with_writer(io::stdout)
            .with_timer(ChronoUtc::rfc_3339())
            .with_target(config.show_target)
            .with_thread_ids(config.show_thread_ids)
            .with_line_number(config.show_line_number)
            .with_ansi(true);

        registry.with(console_layer).init();
    }

    Ok(())
}

/// 初始化默认日志配置
pub fn init_default_logging() -> Result<(), Box<dyn std::error::Error>> {
    init_logging(LogConfig::default())
}

/// 初始化开发环境日志配置
pub fn init_dev_logging() -> Result<(), Box<dyn std::error::Error>> {
    let config = LogConfig {
        level: Level::DEBUG,
        log_to_file: false,
        show_target: true,
        show_thread_ids: true,
        show_line_number: true,
        ..Default::default()
    };
    init_logging(config)
}

/// 初始化生产环境日志配置
pub fn init_prod_logging() -> Result<(), Box<dyn std::error::Error>> {
    let config = LogConfig {
        level: Level::INFO,
        log_to_file: true,
        log_dir: "/var/log/trading-ui".to_string(),
        rotation: Rotation::DAILY,
        show_target: false,
        show_thread_ids: false,
        show_line_number: false,
        ..Default::default()
    };
    init_logging(config)
}

/// 日志宏的便捷重导出
pub use tracing::{debug, error, info, trace, warn};

/// 结构化日志宏
#[macro_export]
macro_rules! log_trade {
    ($level:ident, $exchange:expr, $symbol:expr, $side:expr, $quantity:expr, $price:expr) => {
        tracing::$level!(
            exchange = $exchange,
            symbol = $symbol,
            side = $side,
            quantity = $quantity,
            price = $price,
            "Trade executed"
        );
    };
}

#[macro_export]
macro_rules! log_order {
    ($level:ident, $order_id:expr, $exchange:expr, $symbol:expr, $side:expr, $quantity:expr, $price:expr, $order_type:expr) => {
        tracing::$level!(
            order_id = $order_id,
            exchange = $exchange,
            symbol = $symbol,
            side = $side,
            quantity = $quantity,
            price = $price,
            order_type = $order_type,
            "Order placed"
        );
    };
}

#[macro_export]
macro_rules! log_position {
    ($level:ident, $exchange:expr, $symbol:expr, $size:expr, $entry_price:expr, $unrealized_pnl:expr) => {
        tracing::$level!(
            exchange = $exchange,
            symbol = $symbol,
            size = $size,
            entry_price = $entry_price,
            unrealized_pnl = $unrealized_pnl,
            "Position updated"
        );
    };
}

#[macro_export]
macro_rules! log_websocket {
    ($level:ident, $event:expr, $message:expr) => {
        tracing::$level!(
            event = $event,
            message = $message,
            "WebSocket event"
        );
    };
}

#[macro_export]
macro_rules! log_error {
    ($error:expr, $context:expr) => {
        tracing::error!(
            error = %$error,
            context = $context,
            "Error occurred"
        );
    };
}

/// 性能监控宏
#[macro_export]
macro_rules! measure_time {
    ($name:expr, $code:block) => {{
        let start = std::time::Instant::now();
        let result = $code;
        let duration = start.elapsed();
        tracing::debug!(
            operation = $name,
            duration_ms = duration.as_millis(),
            "Operation completed"
        );
        result
    }};
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing::info;

    #[test]
    fn test_logging_init() {
        // 测试日志初始化
        let config = LogConfig {
            log_to_file: false,
            ..Default::default()
        };
        
        assert!(init_logging(config).is_ok());
        
        // 测试日志输出
        info!("Test log message");
    }
}
