use crossterm::{
    event, execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use dashmap::DashMap;
use ratatui::{
    Terminal,
    backend::CrosstermBackend,
    layout::{Constraint, Direction, Layout},
    widgets::{Block, Borders, Paragraph},
};
use serde::Deserialize;
use std::sync::Arc;
use std::{
    error::Error,
    io,
    time::{Duration, Instant},
};
use tokio::{select, sync::broadcast, time::sleep};

// ---------------- Domain types ----------------
#[derive(Debug, Default, <PERSON>lone)]
pub struct OrderBook {
    pub bids: Vec<(f64, f64)>,
    pub asks: Vec<(f64, f64)>,
    pub last_update_id: u64,
}

#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON>lone)]
pub struct Trade {
    pub price: f64,
    pub qty: f64,
    pub is_buyer_maker: bool,
    pub ts: u64,
}

#[derive(Debug, Default)]
pub struct AppState {
    spot_books: DashMap<String, OrderBook>, // symbol -> orderbook
    spot_trades: DashMap<String, Vec<Trade>>, // recent trades (rolling window)
    spot_unreal_pnl: f64,
    fut_books: DashMap<String, OrderBook>,
    fut_trades: DashMap<String, Vec<Trade>>, // my trades
    fut_unreal_pnl: f64,
}

type SharedState = Arc<AppState>;

// ---------------- Async event messages ----------------
#[derive(Debug, Clone)]
enum UiEvent {
    Tick,
    Resize(u16, u16),
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // === 0. shared state ===
    let state: SharedState = Arc::new(AppState::default());

    // === 1. spawn data tasks ===
    let (ui_tx, mut ui_rx) = broadcast::channel::<UiEvent>(16);

    let symbols = vec!["btcusdt", "ethusdt"];

    // 1.1 Binance Spot depth stream
    let spot_state = state.clone();
    tokio::spawn(async move {
        if let Err(e) = spawn_spot_depth_ws(spot_state, symbols).await {
            eprintln!("[WS‑Spot] error: {e}");
        }
    });

    // 1.2 UI ticker (60 FPS ≈16ms)
    tokio::spawn(async move {
        loop {
            let _ = ui_tx.send(UiEvent::Tick);
            sleep(Duration::from_millis(16)).await;
        }
    });

    // === 2. terminal init ===
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // === 3. main UI loop ===
    let mut last_tick = Instant::now();
    loop {
        // 3.1 draw
        terminal.draw(|f| ui(f, &state))?;

        // 3.2 event handling
        let timeout = Duration::from_millis(16).saturating_sub(last_tick.elapsed());
        select! {
            biased;
            Ok(ev) = ui_rx.recv() => {
                match ev {
                    UiEvent::Tick => last_tick = Instant::now(),
                    UiEvent::Resize(_, _) => {},
                }
            }
            Ok(true) = async { Ok(event::poll(timeout)?) } => {
                if let event::Event::Key(key) = event::read()? {
                    if key.code == event::KeyCode::Char('q') { break; }
                }
            }
        }
    }

    // === 4. teardown ===
    disable_raw_mode()?;
    execute!(terminal.backend_mut(), LeaveAlternateScreen)?;
    terminal.show_cursor()?;
    Ok(())
}

// ---------------- UI render ----------------
fn ui<B: ratatui::backend::Backend>(f: &mut ratatui::Frame<B>, state: &SharedState) {
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .margin(1)
        .constraints([
            Constraint::Length(10), // Spot
            Constraint::Length(10), // Futures
            Constraint::Min(10),    // All exchanges positions
            Constraint::Length(2),  // Status bar
        ])
        .split(f.size());

    let spot_block = Block::default().title("BN Spot").borders(Borders::ALL);
    f.render_widget(spot_block, chunks[0]);

    let fut_block = Block::default().title("BN Futures").borders(Borders::ALL);
    f.render_widget(fut_block, chunks[1]);

    let ex_block = Block::default()
        .title("Positions / PnL")
        .borders(Borders::ALL);
    f.render_widget(ex_block, chunks[2]);

    let status = Paragraph::new("q = quit | TODO: commands …");
    f.render_widget(status, chunks[3]);
}

// ---------------- Data tasks ----------------
async fn spawn_spot_depth_ws(state: SharedState, symbols: Vec<&str>) -> Result<(), Box<dyn Error>> {
    // Join multiple symbol streams into a single multiplexed stream url
    let streams: Vec<String> = symbols
        .iter()
        .map(|s| format!("{s}@depth5@100ms"))
        .collect();
    let url = format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    );

    let mut stream = MarketStream::<DepthStream>::connect(&url).await?;

    while let Some(msg) = stream.next().await {
        match msg {
            Ok(event) => handle_depth_event(&state, event.data).await,
            Err(e) => eprintln!("[depth] {e}"),
        }
    }
    Ok(())
}

async fn handle_depth_event(state: &SharedState, depth: DepthStream) {
    let ob = OrderBook {
        bids: depth
            .bids
            .iter()
            .map(|b| (b.price.parse().unwrap_or(0.0), b.qty.parse().unwrap_or(0.0)))
            .collect(),
        asks: depth
            .asks
            .iter()
            .map(|a| (a.price.parse().unwrap_or(0.0), a.qty.parse().unwrap_or(0.0)))
            .collect(),
        last_update_id: depth.last_update_id,
    };
    state.spot_books.insert(depth.symbol.to_lowercase(), ob);
}
