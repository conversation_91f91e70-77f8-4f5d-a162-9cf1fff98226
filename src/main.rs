use chrono::{DateTime, Utc};
use crossterm::{
    event::{self, Event, KeyCode, KeyEvent},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use dashmap::DashMap;
use futures_util::{SinkExt, StreamExt};
use ratatui::{
    Frame, Terminal,
    backend::CrosstermBackend,
    layout::{Alignment, Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Paragraph, Row, Table, Wrap},
};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::Arc;
use std::{
    error::Error,
    io,
    time::{Duration, Instant},
};
use tokio::{
    net::{TcpListener, TcpStream},
    select,
    sync::{broadcast, mpsc},
    time::sleep,
};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use uuid::Uuid;

// ---------------- Domain types ----------------
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub bids: Vec<(f64, f64)>, // price, quantity
    pub asks: Vec<(f64, f64)>,
    pub last_update_id: u64,
    pub symbol: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    pub id: String,
    pub symbol: String,
    pub price: f64,
    pub qty: f64,
    pub side: String, // "BUY" or "SELL"
    pub timestamp: DateTime<Utc>,
    pub is_maker: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub exchange: String,
    pub symbol: String,
    pub size: f64,
    pub entry_price: f64,
    pub mark_price: f64,
    pub unrealized_pnl: f64,
    pub side: String, // "LONG" or "SHORT"
}

#[derive(Debug, Clone)]
pub struct AppState {
    // Binance Spot
    pub spot_books: DashMap<String, OrderBook>,
    pub spot_trades: DashMap<String, VecDeque<Trade>>, // recent market trades
    pub spot_my_trades: DashMap<String, VecDeque<Trade>>, // my trades
    pub spot_unreal_pnl: f64,

    // Binance Futures
    pub fut_books: DashMap<String, OrderBook>,
    pub fut_trades: DashMap<String, VecDeque<Trade>>, // recent market trades
    pub fut_my_trades: DashMap<String, VecDeque<Trade>>, // my trades
    pub fut_unreal_pnl: f64,

    // All exchanges positions
    pub positions: DashMap<String, Position>, // key: exchange_symbol

    // UI state
    pub input_mode: InputMode,
    pub command_input: String,
    pub status_message: String,
    pub selected_tab: usize,
}

#[derive(Debug, Clone, PartialEq)]
pub enum InputMode {
    Normal,
    Command,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            spot_books: DashMap::new(),
            spot_trades: DashMap::new(),
            spot_my_trades: DashMap::new(),
            spot_unreal_pnl: 0.0,
            fut_books: DashMap::new(),
            fut_trades: DashMap::new(),
            fut_my_trades: DashMap::new(),
            fut_unreal_pnl: 0.0,
            positions: DashMap::new(),
            input_mode: InputMode::Normal,
            command_input: String::new(),
            status_message: "Ready".to_string(),
            selected_tab: 0,
        }
    }
}

type SharedState = Arc<AppState>;

// ---------------- WebSocket Messages ----------------
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WsMessage {
    // Market data updates
    OrderBookUpdate {
        data: OrderBook,
    },
    TradeUpdate {
        data: Trade,
    },
    PositionUpdate {
        data: Position,
    },

    // Commands
    PlaceOrder {
        exchange: String,
        symbol: String,
        side: String,
        quantity: f64,
        price: Option<f64>, // None for market orders
        order_type: String, // "MARKET", "LIMIT"
    },
    ClosePosition {
        exchange: String,
        symbol: String,
        quantity: Option<f64>, // None to close all
    },
    Withdraw {
        exchange: String,
        asset: String,
        amount: f64,
        address: String,
    },

    // Responses
    CommandResult {
        success: bool,
        message: String,
        order_id: Option<String>,
    },
}

// ---------------- UI Events ----------------
#[derive(Debug, Clone)]
enum UiEvent {
    Tick,
    Resize(u16, u16),
    KeyPress(KeyEvent),
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // === 0. shared state ===
    let state: SharedState = Arc::new(AppState::default());

    // === 1. spawn background tasks ===
    let (ui_tx, mut ui_rx) = broadcast::channel::<UiEvent>(16);
    let (ws_tx, ws_rx) = mpsc::unbounded_channel::<WsMessage>();

    // 1.1 Mock WebSocket server
    let ws_state = state.clone();
    let ws_sender = ws_tx.clone();
    tokio::spawn(async move {
        if let Err(e) = start_mock_ws_server(ws_state, ws_sender).await {
            eprintln!("[WS-Server] error: {e}");
        }
    });

    // 1.2 Mock data generator
    let mock_state = state.clone();
    tokio::spawn(async move {
        generate_mock_data(mock_state).await;
    });

    // 1.3 UI ticker (60 FPS ≈16ms)
    tokio::spawn(async move {
        loop {
            let _ = ui_tx.send(UiEvent::Tick);
            sleep(Duration::from_millis(16)).await;
        }
    });

    // 1.4 WebSocket message handler
    let msg_state = state.clone();
    tokio::spawn(async move {
        handle_ws_messages(msg_state, ws_rx).await;
    });

    // === 2. terminal init ===
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // === 3. main UI loop ===
    let mut last_tick = Instant::now();
    loop {
        // 3.1 draw
        terminal.draw(|f| ui(f, &state))?;

        // 3.2 event handling
        let timeout = Duration::from_millis(16).saturating_sub(last_tick.elapsed());
        select! {
            biased;
            Ok(ev) = ui_rx.recv() => {
                match ev {
                    UiEvent::Tick => last_tick = Instant::now(),
                    UiEvent::Resize(_, _) => {},
                    UiEvent::KeyPress(key) => {
                        if handle_key_event(&state, key, &ws_tx).await {
                            break;
                        }
                    }
                }
            }
            Ok(true) = async { Ok(event::poll(timeout)?) } => {
                if let Event::Key(key) = event::read()? {
                    let _ = ui_tx.send(UiEvent::KeyPress(key));
                }
            }
        }
    }

    // === 4. teardown ===
    disable_raw_mode()?;
    execute!(terminal.backend_mut(), LeaveAlternateScreen)?;
    terminal.show_cursor()?;
    Ok(())
}

// ---------------- Key Event Handler ----------------
async fn handle_key_event(
    state: &SharedState,
    key: KeyEvent,
    ws_tx: &mpsc::UnboundedSender<WsMessage>,
) -> bool {
    match state.input_mode {
        InputMode::Normal => match key.code {
            KeyCode::Char('q') => return true,
            KeyCode::Char(':') => {
                state.input_mode = InputMode::Command;
                state.command_input.clear();
            }
            KeyCode::Tab => {
                state.selected_tab = (state.selected_tab + 1) % 3;
            }
            _ => {}
        },
        InputMode::Command => match key.code {
            KeyCode::Enter => {
                let command = state.command_input.clone();
                state.command_input.clear();
                state.input_mode = InputMode::Normal;

                if let Some(msg) = parse_command(&command) {
                    let _ = ws_tx.send(msg);
                    state.status_message = format!("Sent command: {}", command);
                } else {
                    state.status_message = format!("Invalid command: {}", command);
                }
            }
            KeyCode::Esc => {
                state.input_mode = InputMode::Normal;
                state.command_input.clear();
            }
            KeyCode::Char(c) => {
                state.command_input.push(c);
            }
            KeyCode::Backspace => {
                state.command_input.pop();
            }
            _ => {}
        },
    }
    false
}

// ---------------- Command Parser ----------------
fn parse_command(command: &str) -> Option<WsMessage> {
    let parts: Vec<&str> = command.split_whitespace().collect();
    if parts.is_empty() {
        return None;
    }

    match parts[0] {
        "buy" | "sell" => {
            if parts.len() >= 4 {
                let side = parts[0].to_uppercase();
                let exchange = parts[1].to_string();
                let symbol = parts[2].to_uppercase();
                let quantity: f64 = parts[3].parse().ok()?;
                let price = if parts.len() > 4 {
                    parts[4].parse().ok()
                } else {
                    None
                };

                Some(WsMessage::PlaceOrder {
                    exchange,
                    symbol,
                    side,
                    quantity,
                    price,
                    order_type: if price.is_some() {
                        "LIMIT".to_string()
                    } else {
                        "MARKET".to_string()
                    },
                })
            } else {
                None
            }
        }
        "close" => {
            if parts.len() >= 3 {
                let exchange = parts[1].to_string();
                let symbol = parts[2].to_uppercase();
                let quantity = if parts.len() > 3 {
                    parts[3].parse().ok()
                } else {
                    None
                };

                Some(WsMessage::ClosePosition {
                    exchange,
                    symbol,
                    quantity,
                })
            } else {
                None
            }
        }
        "withdraw" => {
            if parts.len() >= 5 {
                let exchange = parts[1].to_string();
                let asset = parts[2].to_uppercase();
                let amount: f64 = parts[3].parse().ok()?;
                let address = parts[4].to_string();

                Some(WsMessage::Withdraw {
                    exchange,
                    asset,
                    amount,
                    address,
                })
            } else {
                None
            }
        }
        _ => None,
    }
}

// ---------------- UI Render ----------------
fn ui<B: ratatui::backend::Backend>(f: &mut Frame<B>, state: &SharedState) {
    let main_chunks = Layout::default()
        .direction(Direction::Vertical)
        .margin(1)
        .constraints([
            Constraint::Length(1), // Tab bar
            Constraint::Min(0),    // Main content
            Constraint::Length(3), // Command input / status
        ])
        .split(f.size());

    // Tab bar
    render_tab_bar(f, main_chunks[0], state.selected_tab);

    // Main content based on selected tab
    match state.selected_tab {
        0 => render_binance_tab(f, main_chunks[1], state),
        1 => render_positions_tab(f, main_chunks[1], state),
        2 => render_operations_tab(f, main_chunks[1], state),
        _ => {}
    }

    // Command input / status bar
    render_command_bar(f, main_chunks[2], state);
}

// ---------------- UI Render Functions ----------------
fn render_tab_bar(f: &mut Frame, area: Rect, selected: usize) {
    let tabs = vec!["Binance", "Positions", "Operations"];
    let tab_titles: Vec<Line> = tabs
        .iter()
        .enumerate()
        .map(|(i, &tab)| {
            if i == selected {
                Line::from(Span::styled(
                    format!(" {} ", tab),
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD),
                ))
            } else {
                Line::from(Span::styled(
                    format!(" {} ", tab),
                    Style::default().fg(Color::White),
                ))
            }
        })
        .collect();

    let tabs_widget = Paragraph::new(tab_titles.join(Line::from(" | ")))
        .style(Style::default().fg(Color::White))
        .alignment(Alignment::Left);
    f.render_widget(tabs_widget, area);
}

fn render_binance_tab(f: &mut Frame, area: Rect, state: &SharedState) {
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Percentage(50), // Spot
            Constraint::Percentage(50), // Futures
        ])
        .split(area);

    // Spot section
    let spot_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(40), // Depth
            Constraint::Percentage(30), // Trades
            Constraint::Percentage(30), // PnL
        ])
        .split(chunks[0]);

    render_orderbook(f, spot_chunks[0], "BN Spot Depth", &state.spot_books);
    render_trades(f, spot_chunks[1], "Market Trades", &state.spot_trades);
    render_pnl(f, spot_chunks[2], "Spot PnL", state.spot_unreal_pnl);

    // Futures section
    let fut_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(40), // Depth
            Constraint::Percentage(30), // My Trades
            Constraint::Percentage(30), // PnL
        ])
        .split(chunks[1]);

    render_orderbook(f, fut_chunks[0], "BN Futures Depth", &state.fut_books);
    render_trades(f, fut_chunks[1], "My Trades", &state.fut_my_trades);
    render_pnl(f, fut_chunks[2], "Futures PnL", state.fut_unreal_pnl);
}

fn render_positions_tab(f: &mut Frame, area: Rect, state: &SharedState) {
    let positions: Vec<Position> = state
        .positions
        .iter()
        .map(|entry| entry.value().clone())
        .collect();

    let rows: Vec<Row> = positions
        .iter()
        .map(|pos| {
            Row::new(vec![
                pos.exchange.clone(),
                pos.symbol.clone(),
                format!("{:.4}", pos.size),
                format!("{:.2}", pos.entry_price),
                format!("{:.2}", pos.mark_price),
                format!("{:.2}", pos.unrealized_pnl),
                pos.side.clone(),
            ])
        })
        .collect();

    let table = Table::new(rows)
        .widths(&[
            Constraint::Length(10), // Exchange
            Constraint::Length(10), // Symbol
            Constraint::Length(12), // Size
            Constraint::Length(12), // Entry Price
            Constraint::Length(12), // Mark Price
            Constraint::Length(12), // Unrealized PnL
            Constraint::Length(8),  // Side
        ])
        .header(
            Row::new(vec![
                "Exchange", "Symbol", "Size", "Entry", "Mark", "PnL", "Side",
            ])
            .style(
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
        )
        .block(
            Block::default()
                .title("All Positions")
                .borders(Borders::ALL),
        );

    f.render_widget(table, area);
}

fn render_operations_tab(f: &mut Frame, area: Rect, _state: &SharedState) {
    let help_text = vec![
        "Commands:",
        "",
        "Trading:",
        "  :buy <exchange> <symbol> <qty> [price]   - Place buy order",
        "  :sell <exchange> <symbol> <qty> [price]  - Place sell order",
        "  :close <exchange> <symbol> [qty]         - Close position",
        "",
        "Withdrawal:",
        "  :withdraw <exchange> <asset> <amount> <address>",
        "",
        "Examples:",
        "  :buy binance BTCUSDT 0.1 50000",
        "  :sell okx ETHUSDT 1.0",
        "  :close binance BTCUSDT",
        "  :withdraw binance BTC 0.5 **********************************",
        "",
        "Navigation:",
        "  Tab - Switch tabs",
        "  q   - Quit",
    ];

    let paragraph = Paragraph::new(help_text.join("\n"))
        .block(
            Block::default()
                .title("Operations Help")
                .borders(Borders::ALL),
        )
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

fn render_orderbook(f: &mut Frame, area: Rect, title: &str, books: &DashMap<String, OrderBook>) {
    let mut content = vec!["Symbol | Bid Price | Bid Qty | Ask Price | Ask Qty".to_string()];

    for entry in books.iter() {
        let symbol = entry.key();
        let book = entry.value();

        let best_bid = book
            .bids
            .first()
            .map(|(p, q)| (p, q))
            .unwrap_or((&0.0, &0.0));
        let best_ask = book
            .asks
            .first()
            .map(|(p, q)| (p, q))
            .unwrap_or((&0.0, &0.0));

        content.push(format!(
            "{:>6} | {:>9.2} | {:>7.4} | {:>9.2} | {:>7.4}",
            symbol.to_uppercase(),
            best_bid.0,
            best_bid.1,
            best_ask.0,
            best_ask.1
        ));
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title(title).borders(Borders::ALL))
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

fn render_trades(
    f: &mut Frame,
    area: Rect,
    title: &str,
    trades: &DashMap<String, VecDeque<Trade>>,
) {
    let mut content = vec!["Symbol | Side | Price | Qty | Time".to_string()];

    for entry in trades.iter() {
        let symbol = entry.key();
        let trade_list = entry.value();

        for trade in trade_list.iter().take(5) {
            // Show last 5 trades
            content.push(format!(
                "{:>6} | {:>4} | {:>8.2} | {:>6.4} | {}",
                symbol.to_uppercase(),
                trade.side,
                trade.price,
                trade.qty,
                trade.timestamp.format("%H:%M:%S")
            ));
        }
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title(title).borders(Borders::ALL))
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

fn render_pnl(f: &mut Frame, area: Rect, title: &str, pnl: f64) {
    let color = if pnl >= 0.0 { Color::Green } else { Color::Red };
    let sign = if pnl >= 0.0 { "+" } else { "" };

    let paragraph = Paragraph::new(format!("{}{:.2} USDT", sign, pnl))
        .style(Style::default().fg(color).add_modifier(Modifier::BOLD))
        .block(Block::default().title(title).borders(Borders::ALL))
        .alignment(Alignment::Center);

    f.render_widget(paragraph, area);
}

fn render_command_bar(f: &mut Frame, area: Rect, state: &SharedState) {
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Length(1), // Command input
            Constraint::Length(1), // Status
        ])
        .split(area);

    // Command input
    let input_style = if state.input_mode == InputMode::Command {
        Style::default().fg(Color::Yellow)
    } else {
        Style::default().fg(Color::Gray)
    };

    let input_text = if state.input_mode == InputMode::Command {
        format!(":{}", state.command_input)
    } else {
        "Press ':' to enter command mode, Tab to switch tabs, 'q' to quit".to_string()
    };

    let input = Paragraph::new(input_text)
        .style(input_style)
        .block(Block::default().borders(Borders::ALL));

    f.render_widget(input, chunks[0]);

    // Status
    let status = Paragraph::new(state.status_message.clone())
        .style(Style::default().fg(Color::Cyan))
        .block(Block::default().borders(Borders::ALL));

    f.render_widget(status, chunks[1]);
}

// ---------------- WebSocket Server ----------------
async fn start_mock_ws_server(
    state: SharedState,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
) -> Result<(), Box<dyn Error>> {
    let listener = TcpListener::bind("127.0.0.1:8080").await?;
    println!("Mock WebSocket server listening on ws://127.0.0.1:8080");

    while let Ok((stream, _)) = listener.accept().await {
        let state_clone = state.clone();
        let ws_tx_clone = ws_tx.clone();

        tokio::spawn(async move {
            if let Err(e) = handle_ws_connection(stream, state_clone, ws_tx_clone).await {
                eprintln!("WebSocket connection error: {}", e);
            }
        });
    }

    Ok(())
}

async fn handle_ws_connection(
    stream: TcpStream,
    _state: SharedState,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
) -> Result<(), Box<dyn Error>> {
    let ws_stream = accept_async(stream).await?;
    let (mut ws_sender, mut ws_receiver) = ws_stream.split();

    // Handle incoming messages
    while let Some(msg) = ws_receiver.next().await {
        match msg? {
            Message::Text(text) => {
                if let Ok(ws_msg) = serde_json::from_str::<WsMessage>(&text) {
                    match ws_msg {
                        WsMessage::PlaceOrder { .. }
                        | WsMessage::ClosePosition { .. }
                        | WsMessage::Withdraw { .. } => {
                            // Send command to handler
                            let _ = ws_tx.send(ws_msg);

                            // Send response
                            let response = WsMessage::CommandResult {
                                success: true,
                                message: "Command executed successfully".to_string(),
                                order_id: Some(Uuid::new_v4().to_string()),
                            };

                            let response_text = serde_json::to_string(&response)?;
                            ws_sender.send(Message::Text(response_text)).await?;
                        }
                        _ => {}
                    }
                }
            }
            Message::Close(_) => break,
            _ => {}
        }
    }

    Ok(())
}

// ---------------- Message Handler ----------------
async fn handle_ws_messages(state: SharedState, mut ws_rx: mpsc::UnboundedReceiver<WsMessage>) {
    while let Some(msg) = ws_rx.recv().await {
        match msg {
            WsMessage::PlaceOrder {
                exchange,
                symbol,
                side,
                quantity,
                price,
                order_type,
            } => {
                state.status_message = format!(
                    "Placed {} order: {} {} {} @ {}",
                    order_type,
                    side,
                    quantity,
                    symbol,
                    price
                        .map(|p| p.to_string())
                        .unwrap_or_else(|| "MARKET".to_string())
                );

                // Simulate trade execution
                let trade = Trade {
                    id: Uuid::new_v4().to_string(),
                    symbol: symbol.clone(),
                    price: price.unwrap_or(50000.0), // Mock price
                    qty: quantity,
                    side,
                    timestamp: Utc::now(),
                    is_maker: false,
                };

                if exchange == "binance" {
                    state
                        .fut_my_trades
                        .entry(symbol.to_lowercase())
                        .or_insert_with(VecDeque::new)
                        .push_front(trade);
                }
            }
            WsMessage::ClosePosition {
                exchange,
                symbol,
                quantity,
            } => {
                state.status_message = format!(
                    "Closed position: {} {} {}",
                    exchange,
                    symbol,
                    quantity
                        .map(|q| q.to_string())
                        .unwrap_or_else(|| "ALL".to_string())
                );
            }
            WsMessage::Withdraw {
                exchange,
                asset,
                amount,
                address,
            } => {
                state.status_message = format!(
                    "Withdraw: {} {} from {} to {}",
                    amount,
                    asset,
                    exchange,
                    &address[..8]
                );
            }
            _ => {}
        }
    }
}

// ---------------- Mock Data Generator ----------------
async fn generate_mock_data(state: SharedState) {
    let symbols = vec!["btcusdt", "ethusdt", "adausdt"];
    let exchanges = vec!["binance", "okx", "bybit"];

    // Initialize some positions
    for exchange in &exchanges {
        for symbol in &symbols {
            let position = Position {
                exchange: exchange.to_string(),
                symbol: symbol.to_uppercase(),
                size: (rand::random::<f64>() - 0.5) * 10.0, // Random position between -5 and 5
                entry_price: 40000.0 + rand::random::<f64>() * 20000.0, // Random price between 40k-60k
                mark_price: 45000.0 + rand::random::<f64>() * 10000.0,  // Random mark price
                unrealized_pnl: (rand::random::<f64>() - 0.5) * 1000.0, // Random PnL
                side: if rand::random::<bool>() {
                    "LONG".to_string()
                } else {
                    "SHORT".to_string()
                },
            };

            state
                .positions
                .insert(format!("{}_{}", exchange, symbol), position);
        }
    }

    loop {
        // Update orderbooks
        for symbol in &symbols {
            let base_price = match symbol.as_str() {
                "btcusdt" => 45000.0,
                "ethusdt" => 3000.0,
                "adausdt" => 0.5,
                _ => 100.0,
            };

            let spread = base_price * 0.001; // 0.1% spread
            let bid_price = base_price - spread / 2.0;
            let ask_price = base_price + spread / 2.0;

            // Generate random orderbook
            let mut bids = Vec::new();
            let mut asks = Vec::new();

            for i in 0..5 {
                let bid_level = bid_price - (i as f64 * spread * 0.1);
                let ask_level = ask_price + (i as f64 * spread * 0.1);
                let qty = rand::random::<f64>() * 10.0 + 1.0;

                bids.push((bid_level, qty));
                asks.push((ask_level, qty));
            }

            let orderbook = OrderBook {
                symbol: symbol.to_string(),
                bids,
                asks,
                last_update_id: rand::random::<u64>(),
            };

            // Update both spot and futures
            state
                .spot_books
                .insert(symbol.to_string(), orderbook.clone());
            state.fut_books.insert(symbol.to_string(), orderbook);
        }

        // Generate random trades
        for symbol in &symbols {
            let base_price = match symbol.as_str() {
                "btcusdt" => 45000.0,
                "ethusdt" => 3000.0,
                "adausdt" => 0.5,
                _ => 100.0,
            };

            let trade = Trade {
                id: Uuid::new_v4().to_string(),
                symbol: symbol.to_uppercase(),
                price: base_price + (rand::random::<f64>() - 0.5) * base_price * 0.01,
                qty: rand::random::<f64>() * 5.0 + 0.1,
                side: if rand::random::<bool>() {
                    "BUY".to_string()
                } else {
                    "SELL".to_string()
                },
                timestamp: Utc::now(),
                is_maker: rand::random::<bool>(),
            };

            // Add to spot trades
            state
                .spot_trades
                .entry(symbol.to_string())
                .or_insert_with(VecDeque::new)
                .push_front(trade.clone());

            // Keep only last 10 trades
            if let Some(mut trades) = state.spot_trades.get_mut(symbol) {
                trades.truncate(10);
            }
        }

        // Update PnL
        state.spot_unreal_pnl = (rand::random::<f64>() - 0.5) * 2000.0;
        state.fut_unreal_pnl = (rand::random::<f64>() - 0.5) * 5000.0;

        // Update positions PnL
        for mut entry in state.positions.iter_mut() {
            let position = entry.value_mut();
            position.mark_price += (rand::random::<f64>() - 0.5) * position.mark_price * 0.001;
            position.unrealized_pnl = (position.mark_price - position.entry_price) * position.size;
        }

        sleep(Duration::from_millis(500)).await; // Update every 500ms
    }
}

// Simple random number generator for mock data
mod rand {
    use std::sync::atomic::{AtomicU64, Ordering};

    static SEED: AtomicU64 = AtomicU64::new(1);

    pub fn random<T>() -> T
    where
        T: From<f64>,
    {
        let seed = SEED.load(Ordering::Relaxed);
        let next = seed.wrapping_mul(1103515245).wrapping_add(12345);
        SEED.store(next, Ordering::Relaxed);

        let normalized = (next as f64) / (u64::MAX as f64);
        T::from(normalized)
    }
}
