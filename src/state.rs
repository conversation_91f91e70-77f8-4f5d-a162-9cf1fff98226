use crate::types::{InputMode, OrderBook, Position, Trade};
use dashmap::DashMap;
use std::collections::VecDeque;
use std::sync::Arc;

/// 应用程序状态
#[derive(Debug, Clone)]
pub struct AppState {
    // Binance Spot
    pub spot_books: DashMap<String, OrderBook>,
    pub spot_trades: DashMap<String, VecDeque<Trade>>, // recent market trades
    pub spot_my_trades: DashMap<String, VecDeque<Trade>>, // my trades
    pub spot_unreal_pnl: f64,
    
    // Binance Futures
    pub fut_books: DashMap<String, OrderBook>,
    pub fut_trades: DashMap<String, VecDeque<Trade>>, // recent market trades
    pub fut_my_trades: DashMap<String, VecDeque<Trade>>, // my trades
    pub fut_unreal_pnl: f64,
    
    // All exchanges positions
    pub positions: DashMap<String, Position>, // key: exchange_symbol
    
    // UI state
    pub input_mode: InputMode,
    pub command_input: String,
    pub status_message: String,
    pub selected_tab: usize,
    
    // Application settings
    pub max_trades_history: usize,
    pub update_interval_ms: u64,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            spot_books: DashMap::new(),
            spot_trades: DashMap::new(),
            spot_my_trades: DashMap::new(),
            spot_unreal_pnl: 0.0,
            fut_books: DashMap::new(),
            fut_trades: DashMap::new(),
            fut_my_trades: DashMap::new(),
            fut_unreal_pnl: 0.0,
            positions: DashMap::new(),
            input_mode: InputMode::Normal,
            command_input: String::new(),
            status_message: "Ready".to_string(),
            selected_tab: 0,
            max_trades_history: 50,
            update_interval_ms: 500,
        }
    }
}

impl AppState {
    /// 创建新的应用状态
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 设置状态消息
    pub fn set_status(&self, message: String) {
        // Note: This is a simplified version. In a real implementation,
        // you might want to use interior mutability patterns like RefCell or Mutex
        // for thread-safe updates to individual fields.
    }
    
    /// 清理旧的交易记录
    pub fn cleanup_old_trades(&self) {
        let max_history = self.max_trades_history;
        
        // Clean spot trades
        for mut entry in self.spot_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }
        
        // Clean spot my trades
        for mut entry in self.spot_my_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }
        
        // Clean futures trades
        for mut entry in self.fut_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }
        
        // Clean futures my trades
        for mut entry in self.fut_my_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }
    }
    
    /// 获取总的未实现盈亏
    pub fn get_total_unrealized_pnl(&self) -> f64 {
        let spot_pnl = self.spot_unreal_pnl;
        let fut_pnl = self.fut_unreal_pnl;
        let positions_pnl: f64 = self.positions
            .iter()
            .map(|entry| entry.value().unrealized_pnl)
            .sum();
        
        spot_pnl + fut_pnl + positions_pnl
    }
    
    /// 获取指定交易所的持仓
    pub fn get_positions_by_exchange(&self, exchange: &str) -> Vec<Position> {
        self.positions
            .iter()
            .filter(|entry| entry.value().exchange == exchange)
            .map(|entry| entry.value().clone())
            .collect()
    }
    
    /// 获取指定交易对的持仓
    pub fn get_positions_by_symbol(&self, symbol: &str) -> Vec<Position> {
        self.positions
            .iter()
            .filter(|entry| entry.value().symbol == symbol)
            .map(|entry| entry.value().clone())
            .collect()
    }
    
    /// 更新订单簿
    pub fn update_orderbook(&self, symbol: &str, orderbook: OrderBook, is_futures: bool) {
        if is_futures {
            self.fut_books.insert(symbol.to_string(), orderbook);
        } else {
            self.spot_books.insert(symbol.to_string(), orderbook);
        }
    }
    
    /// 添加交易记录
    pub fn add_trade(&self, symbol: &str, trade: Trade, is_futures: bool, is_my_trade: bool) {
        let trades_map = match (is_futures, is_my_trade) {
            (false, false) => &self.spot_trades,
            (false, true) => &self.spot_my_trades,
            (true, false) => &self.fut_trades,
            (true, true) => &self.fut_my_trades,
        };
        
        trades_map
            .entry(symbol.to_string())
            .or_insert_with(VecDeque::new)
            .push_front(trade);
    }
    
    /// 更新持仓
    pub fn update_position(&self, key: &str, position: Position) {
        self.positions.insert(key.to_string(), position);
    }
    
    /// 删除持仓
    pub fn remove_position(&self, key: &str) {
        self.positions.remove(key);
    }
}

pub type SharedState = Arc<AppState>;
