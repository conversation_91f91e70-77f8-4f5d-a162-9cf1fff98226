use crate::types::WsMessage;
use crate::state::SharedState;
use futures_util::{SinkExt, StreamExt};
use std::error::Error;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::mpsc;
use tokio_tungstenite::{accept_async, tungstenite::Message};
use uuid::Uuid;

/// WebSocket服务器
pub struct WebSocketServer {
    addr: String,
    state: SharedState,
}

impl WebSocketServer {
    /// 创建新的WebSocket服务器
    pub fn new(addr: String, state: SharedState) -> Self {
        Self { addr, state }
    }
    
    /// 启动WebSocket服务器
    pub async fn start(&self, ws_tx: mpsc::UnboundedSender<WsMessage>) -> Result<(), Box<dyn Error>> {
        let listener = TcpListener::bind(&self.addr).await?;
        println!("Mock WebSocket server listening on ws://{}", self.addr);

        while let Ok((stream, addr)) = listener.accept().await {
            println!("New WebSocket connection from: {}", addr);
            
            let state_clone = self.state.clone();
            let ws_tx_clone = ws_tx.clone();
            
            tokio::spawn(async move {
                if let Err(e) = handle_connection(stream, state_clone, ws_tx_clone).await {
                    eprintln!("WebSocket connection error: {}", e);
                }
            });
        }
        
        Ok(())
    }
}

/// 处理单个WebSocket连接
async fn handle_connection(
    stream: TcpStream,
    _state: SharedState,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
) -> Result<(), Box<dyn Error>> {
    let ws_stream = accept_async(stream).await?;
    let (mut ws_sender, mut ws_receiver) = ws_stream.split();

    println!("WebSocket connection established");

    // 处理传入的消息
    while let Some(msg) = ws_receiver.next().await {
        match msg? {
            Message::Text(text) => {
                println!("Received message: {}", text);
                
                if let Ok(ws_msg) = serde_json::from_str::<WsMessage>(&text) {
                    match ws_msg {
                        WsMessage::PlaceOrder { .. } 
                        | WsMessage::ClosePosition { .. } 
                        | WsMessage::Withdraw { .. } => {
                            // 发送命令到处理器
                            if let Err(e) = ws_tx.send(ws_msg) {
                                eprintln!("Failed to send command to handler: {}", e);
                                break;
                            }
                            
                            // 发送响应
                            let response = WsMessage::CommandResult {
                                success: true,
                                message: "Command executed successfully".to_string(),
                                order_id: Some(Uuid::new_v4().to_string()),
                            };
                            
                            let response_text = serde_json::to_string(&response)?;
                            if let Err(e) = ws_sender.send(Message::Text(response_text)).await {
                                eprintln!("Failed to send response: {}", e);
                                break;
                            }
                        }
                        _ => {
                            println!("Received non-command message: {:?}", ws_msg);
                        }
                    }
                } else {
                    eprintln!("Failed to parse WebSocket message: {}", text);
                    
                    // 发送错误响应
                    let error_response = WsMessage::CommandResult {
                        success: false,
                        message: "Invalid message format".to_string(),
                        order_id: None,
                    };
                    
                    let response_text = serde_json::to_string(&error_response)?;
                    if let Err(e) = ws_sender.send(Message::Text(response_text)).await {
                        eprintln!("Failed to send error response: {}", e);
                        break;
                    }
                }
            }
            Message::Binary(_) => {
                println!("Received binary message (not supported)");
            }
            Message::Ping(payload) => {
                println!("Received ping, sending pong");
                if let Err(e) = ws_sender.send(Message::Pong(payload)).await {
                    eprintln!("Failed to send pong: {}", e);
                    break;
                }
            }
            Message::Pong(_) => {
                println!("Received pong");
            }
            Message::Close(_) => {
                println!("WebSocket connection closed by client");
                break;
            }
            Message::Frame(_) => {
                // 内部帧，通常不需要处理
            }
        }
    }
    
    println!("WebSocket connection terminated");
    Ok(())
}

/// 启动模拟WebSocket服务器
pub async fn start_mock_server(
    state: SharedState,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
) -> Result<(), Box<dyn Error>> {
    let server = WebSocketServer::new("127.0.0.1:8080".to_string(), state);
    server.start(ws_tx).await
}
