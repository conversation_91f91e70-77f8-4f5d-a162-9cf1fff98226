use crate::types::{WsMessage, Trade};
use crate::state::SharedState;
use chrono::Utc;
use std::collections::VecDeque;
use tokio::sync::mpsc;
use uuid::Uuid;

/// WebSocket消息处理器
pub struct MessageHandler {
    state: SharedState,
}

impl MessageHandler {
    /// 创建新的消息处理器
    pub fn new(state: SharedState) -> Self {
        Self { state }
    }
    
    /// 启动消息处理循环
    pub async fn start(&self, mut ws_rx: mpsc::UnboundedReceiver<WsMessage>) {
        while let Some(msg) = ws_rx.recv().await {
            self.handle_message(msg).await;
        }
    }
    
    /// 处理单个消息
    async fn handle_message(&self, msg: WsMessage) {
        match msg {
            WsMessage::PlaceOrder { 
                exchange, 
                symbol, 
                side, 
                quantity, 
                price, 
                order_type 
            } => {
                self.handle_place_order(exchange, symbol, side, quantity, price, order_type).await;
            }
            WsMessage::ClosePosition { 
                exchange, 
                symbol, 
                quantity 
            } => {
                self.handle_close_position(exchange, symbol, quantity).await;
            }
            WsMessage::Withdraw { 
                exchange, 
                asset, 
                amount, 
                address 
            } => {
                self.handle_withdraw(exchange, asset, amount, address).await;
            }
            _ => {
                println!("Unhandled message type: {:?}", msg);
            }
        }
    }
    
    /// 处理下单命令
    async fn handle_place_order(
        &self,
        exchange: String,
        symbol: String,
        side: String,
        quantity: f64,
        price: Option<f64>,
        order_type: String,
    ) {
        println!("Processing order: {} {} {} {} @ {:?}", 
                 order_type, side, quantity, symbol, price);
        
        // 更新状态消息
        let status_msg = format!(
            "Placed {} order: {} {} {} @ {}",
            order_type,
            side,
            quantity,
            symbol,
            price.map(|p| p.to_string()).unwrap_or_else(|| "MARKET".to_string())
        );
        
        // 注意：这里我们需要一个更好的方式来更新状态消息
        // 在实际实现中，可能需要使用消息传递或其他同步机制
        println!("Status: {}", status_msg);
        
        // 模拟交易执行
        let execution_price = price.unwrap_or_else(|| {
            // 模拟市价单的执行价格
            match symbol.as_str() {
                "BTCUSDT" => 45000.0 + (rand::random::<f64>() - 0.5) * 1000.0,
                "ETHUSDT" => 3000.0 + (rand::random::<f64>() - 0.5) * 100.0,
                "ADAUSDT" => 0.5 + (rand::random::<f64>() - 0.5) * 0.1,
                _ => 100.0,
            }
        });
        
        let trade = Trade {
            id: Uuid::new_v4().to_string(),
            symbol: symbol.clone(),
            price: execution_price,
            qty: quantity,
            side,
            timestamp: Utc::now(),
            is_maker: order_type == "LIMIT",
        };
        
        // 添加到相应的交易记录
        let is_futures = exchange == "binance"; // 简化逻辑
        self.state.add_trade(&symbol.to_lowercase(), trade, is_futures, true);
        
        println!("Order executed at price: {}", execution_price);
    }
    
    /// 处理平仓命令
    async fn handle_close_position(
        &self,
        exchange: String,
        symbol: String,
        quantity: Option<f64>,
    ) {
        println!("Processing close position: {} {} {:?}", exchange, symbol, quantity);
        
        let status_msg = format!(
            "Closed position: {} {} {}",
            exchange,
            symbol,
            quantity.map(|q| q.to_string()).unwrap_or_else(|| "ALL".to_string())
        );
        
        println!("Status: {}", status_msg);
        
        // 在实际实现中，这里会查找现有持仓并执行平仓操作
        // 现在只是模拟
        let position_key = format!("{}_{}", exchange, symbol.to_lowercase());
        if let Some(position) = self.state.positions.get(&position_key) {
            let close_qty = quantity.unwrap_or(position.size.abs());
            println!("Closing {} units of position", close_qty);
            
            // 如果是全部平仓，删除持仓记录
            if quantity.is_none() || close_qty >= position.size.abs() {
                self.state.remove_position(&position_key);
                println!("Position fully closed");
            }
        } else {
            println!("No position found for {}", position_key);
        }
    }
    
    /// 处理提币命令
    async fn handle_withdraw(
        &self,
        exchange: String,
        asset: String,
        amount: f64,
        address: String,
    ) {
        println!("Processing withdrawal: {} {} from {} to {}", 
                 amount, asset, exchange, &address[..8]);
        
        let status_msg = format!(
            "Withdraw: {} {} from {} to {}...",
            amount, asset, exchange, &address[..8]
        );
        
        println!("Status: {}", status_msg);
        
        // 在实际实现中，这里会调用交易所的提币API
        // 现在只是模拟
        println!("Withdrawal request submitted");
    }
}

/// 启动消息处理器
pub async fn start_message_handler(
    state: SharedState,
    ws_rx: mpsc::UnboundedReceiver<WsMessage>,
) {
    let handler = MessageHandler::new(state);
    handler.start(ws_rx).await;
}

// 简单的随机数生成器（用于模拟）
mod rand {
    use std::sync::atomic::{AtomicU64, Ordering};
    
    static SEED: AtomicU64 = AtomicU64::new(1);
    
    pub fn random<T>() -> T 
    where 
        T: From<f64>
    {
        let seed = SEED.load(Ordering::Relaxed);
        let next = seed.wrapping_mul(1103515245).wrapping_add(12345);
        SEED.store(next, Ordering::Relaxed);
        
        let normalized = (next as f64) / (u64::MAX as f64);
        T::from(normalized)
    }
}
