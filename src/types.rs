use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;

/// 订单簿数据结构
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub bids: Vec<(f64, f64)>, // price, quantity
    pub asks: Vec<(f64, f64)>,
    pub last_update_id: u64,
    pub symbol: String,
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    pub id: String,
    pub symbol: String,
    pub price: f64,
    pub qty: f64,
    pub side: String, // "BUY" or "SELL"
    pub timestamp: DateTime<Utc>,
    pub is_maker: bool,
}

/// 持仓信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub exchange: String,
    pub symbol: String,
    pub size: f64,
    pub entry_price: f64,
    pub mark_price: f64,
    pub unrealized_pnl: f64,
    pub side: String, // "LONG" or "SHORT"
}

/// WebSocket消息类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WsMessage {
    // Market data updates
    OrderBookUpdate { data: OrderBook },
    TradeUpdate { data: Trade },
    PositionUpdate { data: Position },
    
    // Commands
    PlaceOrder {
        exchange: String,
        symbol: String,
        side: String,
        quantity: f64,
        price: Option<f64>, // None for market orders
        order_type: String, // "MARKET", "LIMIT"
    },
    ClosePosition {
        exchange: String,
        symbol: String,
        quantity: Option<f64>, // None to close all
    },
    Withdraw {
        exchange: String,
        asset: String,
        amount: f64,
        address: String,
    },
    
    // Responses
    CommandResult {
        success: bool,
        message: String,
        order_id: Option<String>,
    },
}

/// UI事件类型
#[derive(Debug, Clone)]
pub enum UiEvent {
    Tick,
    Resize(u16, u16),
    KeyPress(crossterm::event::KeyEvent),
}

/// 输入模式
#[derive(Debug, Clone, PartialEq)]
pub enum InputMode {
    Normal,
    Command,
}

/// 交易所类型
#[derive(Debug, Clone, PartialEq)]
pub enum Exchange {
    Binance,
    Okx,
    Bybit,
    Huobi,
}

impl Exchange {
    pub fn as_str(&self) -> &'static str {
        match self {
            Exchange::Binance => "binance",
            Exchange::Okx => "okx",
            Exchange::Bybit => "bybit",
            Exchange::Huobi => "huobi",
        }
    }
    
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "binance" => Some(Exchange::Binance),
            "okx" => Some(Exchange::Okx),
            "bybit" => Some(Exchange::Bybit),
            "huobi" => Some(Exchange::Huobi),
            _ => None,
        }
    }
}

/// 订单类型
#[derive(Debug, Clone, PartialEq)]
pub enum OrderType {
    Market,
    Limit,
    StopLoss,
    TakeProfit,
}

impl OrderType {
    pub fn as_str(&self) -> &'static str {
        match self {
            OrderType::Market => "MARKET",
            OrderType::Limit => "LIMIT",
            OrderType::StopLoss => "STOP_LOSS",
            OrderType::TakeProfit => "TAKE_PROFIT",
        }
    }
}

/// 订单方向
#[derive(Debug, Clone, PartialEq)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl OrderSide {
    pub fn as_str(&self) -> &'static str {
        match self {
            OrderSide::Buy => "BUY",
            OrderSide::Sell => "SELL",
        }
    }
}

/// 持仓方向
#[derive(Debug, Clone, PartialEq)]
pub enum PositionSide {
    Long,
    Short,
}

impl PositionSide {
    pub fn as_str(&self) -> &'static str {
        match self {
            PositionSide::Long => "LONG",
            PositionSide::Short => "SHORT",
        }
    }
}
