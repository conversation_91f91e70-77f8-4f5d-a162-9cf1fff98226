# Trading UI - CLI交易面板

一个功能完整的CLI交易面板，支持实时数据展示和快速命令操作。

## 功能特性

### 数据展示
1. **BN 现货**
   - 深度数据（买卖盘）
   - 市场成交记录
   - 未实现盈亏

2. **BN 期货**
   - 深度数据（买卖盘）
   - 个人成交记录
   - 未实现盈亏

3. **各交易所持仓**
   - 建仓价格
   - 标记价格
   - 未实现盈亏
   - 持仓方向

### 操作功能
1. **现货/期货开平仓**
   - 市价单/限价单
   - 买入/卖出
   - 平仓操作

2. **提币功能**
   - 支持多交易所
   - 指定地址和数量

## 安装和运行

### 前置要求
- Rust 1.70+
- Python 3.7+ (用于测试客户端)

### 运行交易面板
```bash
# 编译并运行
cargo run

# 或者先编译再运行
cargo build --release
./target/release/trading-ui
```

### 测试WebSocket连接
```bash
# 安装Python依赖
pip install websockets

# 运行测试客户端
python test_client.py
```

## 使用说明

### 界面导航
- **Tab键**: 切换标签页 (Binance / Positions / Operations)
- **q键**: 退出程序
- **:键**: 进入命令模式

### 命令格式

#### 交易命令
```
# 买入 (限价单)
:buy <交易所> <交易对> <数量> <价格>
例如: :buy binance BTCUSDT 0.1 45000

# 卖出 (市价单)
:sell <交易所> <交易对> <数量>
例如: :sell okx ETHUSDT 1.0

# 平仓
:close <交易所> <交易对> [数量]
例如: :close binance BTCUSDT 0.5
例如: :close binance BTCUSDT  # 全部平仓
```

#### 提币命令
```
:withdraw <交易所> <币种> <数量> <地址>
例如: :withdraw binance BTC 0.1 **********************************
```

### 界面布局

#### Binance标签页
```
┌─────────────────────────────────────────────────────────────┐
│ BN Spot Depth    │ Market Trades    │ Spot PnL           │
│ BTCUSDT          │ BTCUSDT BUY      │ +1,234.56 USDT     │
│ 45000.00 | 1.234 │ 45000.00 | 0.1   │                    │
│ 44999.50 | 2.456 │ 44999.50 | 0.2   │                    │
├─────────────────────────────────────────────────────────────┤
│ BN Futures Depth │ My Trades        │ Futures PnL        │
│ BTCUSDT          │ BTCUSDT SELL     │ -567.89 USDT       │
│ 45000.00 | 1.234 │ 45000.00 | 0.1   │                    │
│ 44999.50 | 2.456 │ 44999.50 | 0.2   │                    │
└─────────────────────────────────────────────────────────────┘
```

#### Positions标签页
```
┌─────────────────────────────────────────────────────────────┐
│ Exchange │ Symbol  │ Size     │ Entry    │ Mark     │ PnL    │
│ binance  │ BTCUSDT │ 1.2345   │ 44000.00 │ 45000.00 │ +123.45│
│ okx      │ ETHUSDT │ -2.5000  │ 3100.00  │ 3000.00  │ +250.00│
│ bybit    │ ADAUSDT │ 1000.00  │ 0.45     │ 0.50     │ +50.00 │
└─────────────────────────────────────────────────────────────┘
```

## 技术架构

### 核心组件
- **TUI界面**: 使用ratatui构建的终端用户界面
- **WebSocket服务器**: 监听8080端口，处理交易命令
- **数据生成器**: 模拟实时市场数据
- **状态管理**: 使用DashMap进行线程安全的状态共享

### 数据流
1. 模拟数据生成器 → 应用状态
2. 用户命令 → WebSocket服务器 → 命令处理器
3. 应用状态 → UI渲染器 → 终端显示

### WebSocket API
服务器监听 `ws://localhost:8080`，支持以下消息类型：

#### 下单
```json
{
  "type": "PlaceOrder",
  "exchange": "binance",
  "symbol": "BTCUSDT",
  "side": "BUY",
  "quantity": 0.1,
  "price": 45000.0,
  "order_type": "LIMIT"
}
```

#### 平仓
```json
{
  "type": "ClosePosition",
  "exchange": "binance",
  "symbol": "BTCUSDT",
  "quantity": 0.1
}
```

#### 提币
```json
{
  "type": "Withdraw",
  "exchange": "binance",
  "asset": "BTC",
  "amount": 0.1,
  "address": "**********************************"
}
```

## 开发说明

### 添加新交易所
1. 在 `generate_mock_data` 函数中添加新的交易所名称
2. 在命令解析器中添加对应的处理逻辑
3. 更新UI显示逻辑

### 自定义数据源
替换 `generate_mock_data` 函数，连接到真实的交易所API或数据源。

### 扩展命令
在 `parse_command` 函数中添加新的命令类型和解析逻辑。

## 注意事项

- 当前版本使用模拟数据，不会执行真实交易
- WebSocket服务器仅用于演示，生产环境需要添加认证和安全措施
- 价格和数量仅为示例，实际使用时需要根据交易所规则调整
